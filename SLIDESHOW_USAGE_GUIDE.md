# Provincial Administration Manager - Slideshow System Usage Guide

## Overview

The Provincial Administration Manager now includes a comprehensive slideshow system that allows you to create multiple slideshow groups, tag them with keywords, and display them on different pages using shortcodes.

## Features

- **Multiple Slideshow Groups**: Create unlimited slideshow groups
- **Tag-Based Organization**: Tag slideshows for easy categorization and automatic display
- **Responsive Design**: Works perfectly on all devices
- **Touch/Swipe Support**: Mobile-friendly navigation
- **Autoplay Control**: Enable/disable automatic slide advancement
- **Navigation Controls**: Arrow buttons and dot indicators
- **Image Management**: Integration with WordPress media library
- **Shortcode Support**: Multiple shortcode formats for flexible display

## Getting Started

### 1. Accessing Slideshow Management

1. Log in to your WordPress admin dashboard
2. Navigate to **Provincial Administration** → **Slideshows**
3. You'll see a list of all slideshow groups (empty initially)

### 2. Creating Your First Slideshow Group

1. Click **Add New** or go to **Provincial Administration** → **Add Slideshow**
2. Fill in the form:
   - **Group Name**: A descriptive name (e.g., "Homepage Hero", "About Gallery")
   - **Description**: Optional description for internal reference
   - **Tags**: Comma-separated keywords (e.g., "home, hero, main")
3. Click **Create Slideshow Group**

### 3. Adding Slides to Your Group

1. From the slideshow groups list, click **Manage Slides** for your group
2. In the modal that opens:
   - **Title**: Optional slide title
   - **Description**: Optional slide description
   - **Image**: Click **Select Image** to choose from media library
   - **Link URL**: Optional link when slide is clicked
   - **Order**: Display order (0 = auto-assign to end)
3. Click **Add Slide**
4. Repeat for additional slides

## Using Shortcodes

### Basic Shortcodes

Display a specific slideshow by ID:
```
[esp_slideshow id="1"]
```

Display a slideshow by tag:
```
[esp_slideshow tag="home"]
```

### Advanced Shortcode Options

```
[esp_slideshow tag="hero" autoplay="false" duration="3000" show_nav="true" show_dots="true" height="500"]
```

#### Available Options:

- **id**: Specific slideshow group ID
- **tag**: Tag name to match
- **autoplay**: `true` or `false` (default: `true`)
- **duration**: Milliseconds between slides (default: `5000`)
- **show_nav**: Show navigation arrows - `true` or `false` (default: `true`)
- **show_dots**: Show dot indicators - `true` or `false` (default: `true`)
- **height**: Custom height in pixels (default: `400`)

### Alternative Shortcode Names

The system supports multiple shortcode formats for compatibility:

- `[esp_slideshow]` (recommended)
- `[dakoii_slideshow]`
- `[provincial_slideshow]`
- `[dakoii_prov_admin_slideshow]`

## Tag-Based Display Strategy

Tags allow you to automatically display appropriate slideshows based on page context:

### Example Tag Strategy:

- **home**: Homepage slideshows
- **about**: About page slideshows
- **hero**: Hero/banner slideshows
- **gallery**: Gallery slideshows
- **events**: Event-related slideshows
- **news**: News-related slideshows

### Using Tags Effectively:

1. **Multiple Tags**: Use multiple tags per slideshow (e.g., "home, hero, main")
2. **Specific Pages**: Create tags for specific pages (e.g., "contact-page")
3. **Seasonal Content**: Use date-based tags (e.g., "2024", "christmas")

## Management Features

### Slideshow Groups List

The main slideshow page shows:
- Group name and description
- Associated tags
- Number of slides
- Ready-to-use shortcodes
- Management actions (Edit, Manage Slides, Delete)

### Slide Management

For each slideshow group, you can:
- View all slides with thumbnails
- Add new slides
- Delete existing slides
- Reorder slides (coming in future update)

### Copying Shortcodes

Each slideshow group displays its shortcodes with a **Copy** button for easy use.

## Best Practices

### Image Guidelines

1. **Consistent Dimensions**: Use images with the same aspect ratio
2. **Optimal Size**: Recommended minimum width: 1200px
3. **File Format**: JPG for photos, PNG for graphics with transparency
4. **File Size**: Optimize images to under 500KB for better performance

### Content Guidelines

1. **Slide Count**: 3-7 slides work best for user engagement
2. **Text Length**: Keep titles under 50 characters, descriptions under 150
3. **Autoplay Duration**: 5-8 seconds per slide is optimal
4. **Mobile Consideration**: Test on mobile devices for readability

### Organization Tips

1. **Descriptive Names**: Use clear, descriptive group names
2. **Consistent Tagging**: Develop a tagging strategy and stick to it
3. **Regular Updates**: Keep slideshow content fresh and relevant
4. **Performance**: Don't create too many large slideshows on one page

## Troubleshooting

### Common Issues

**Slideshow not displaying:**
- Check if the shortcode is correct
- Verify the slideshow group has slides
- Ensure the tag exists and matches

**Images not loading:**
- Verify image URLs are correct
- Check if images exist in media library
- Ensure proper file permissions

**Autoplay not working:**
- Check if autoplay is enabled in shortcode
- Verify browser autoplay policies
- Test on different devices

### Getting Help

If you encounter issues:
1. Check the WordPress admin error logs
2. Verify all plugin files are properly uploaded
3. Ensure you have proper user permissions
4. Test with default WordPress theme to rule out theme conflicts

## User Permissions

The slideshow system respects user roles:

- **Administrators**: Full access to all slideshow features
- **Provincial Users**: Can manage provincial-level slideshows
- **District Users**: Can manage district-level slideshows (if enabled)

## Technical Notes

### Database Tables

The system creates two new tables:
- `wp_esp_slideshow_groups`: Stores slideshow group information
- `wp_esp_slides`: Stores individual slide data

### Performance

- Slideshows are optimized for performance
- Images are lazy-loaded when possible
- CSS and JavaScript are only loaded when needed
- Multiple slideshows on one page are supported

### Compatibility

- Works with all modern browsers
- Mobile-responsive design
- Touch/swipe gesture support
- Keyboard navigation support
- Screen reader accessible

## Future Enhancements

Planned features for future updates:
- Drag-and-drop slide reordering
- Slide transition effects
- Video slide support
- Advanced animation options
- Bulk slide import
- Slideshow templates

---

For technical support or feature requests, please contact the plugin administrator.
