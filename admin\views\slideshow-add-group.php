<?php
/**
 * Add Slideshow Group Admin View
 * 
 * @package Provincial_Administration_Manager
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Add New Slideshow Group', 'esp-admin-manager'); ?></h1>
    <a href="<?php echo admin_url('admin.php?page=provincial-admin-slideshows'); ?>" class="page-title-action">
        <?php _e('Back to Slideshows', 'esp-admin-manager'); ?>
    </a>
    <hr class="wp-header-end">

    <?php settings_errors('slideshow_messages'); ?>

    <div id="esp-slideshow-message"></div>

    <form method="post" action="" id="add-group-form">
        <input type="hidden" name="action" value="create_group">
        <?php wp_nonce_field('esp_slideshow_action'); ?>
        
        <table class="form-table">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="group-name"><?php _e('Group Name', 'esp-admin-manager'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" id="group-name" name="name" required class="regular-text" 
                               value="<?php echo isset($_POST['name']) ? esc_attr($_POST['name']) : ''; ?>">
                        <p class="description">
                            <?php _e('Enter a descriptive name for this slideshow group.', 'esp-admin-manager'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="group-description"><?php _e('Description', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="group-description" name="description" class="large-text" rows="5"><?php 
                            echo isset($_POST['description']) ? esc_textarea($_POST['description']) : ''; 
                        ?></textarea>
                        <p class="description">
                            <?php _e('Optional description to help you identify the purpose of this slideshow group.', 'esp-admin-manager'); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="group-tags"><?php _e('Tags', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="group-tags" name="tags" class="regular-text" 
                               placeholder="home, about, contact, hero"
                               value="<?php echo isset($_POST['tags']) ? esc_attr($_POST['tags']) : ''; ?>">
                        <p class="description">
                            <?php _e('Comma-separated tags to identify where this slideshow should appear. These tags can be used in shortcodes to automatically display the appropriate slideshow.', 'esp-admin-manager'); ?>
                        </p>
                        <div class="esp-tags-help">
                            <h4><?php _e('Tag Examples:', 'esp-admin-manager'); ?></h4>
                            <ul>
                                <li><strong>home</strong> - <?php _e('For homepage slideshows', 'esp-admin-manager'); ?></li>
                                <li><strong>about</strong> - <?php _e('For about page slideshows', 'esp-admin-manager'); ?></li>
                                <li><strong>hero</strong> - <?php _e('For hero/banner slideshows', 'esp-admin-manager'); ?></li>
                                <li><strong>gallery</strong> - <?php _e('For gallery slideshows', 'esp-admin-manager'); ?></li>
                                <li><strong>events</strong> - <?php _e('For event-related slideshows', 'esp-admin-manager'); ?></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div class="esp-form-actions">
            <p class="submit">
                <input type="submit" class="button-primary" value="<?php _e('Create Slideshow Group', 'esp-admin-manager'); ?>">
                <a href="<?php echo admin_url('admin.php?page=provincial-admin-slideshows'); ?>" class="button">
                    <?php _e('Cancel', 'esp-admin-manager'); ?>
                </a>
            </p>
        </div>
    </form>

    <div class="esp-help-section">
        <h3><?php _e('What happens next?', 'esp-admin-manager'); ?></h3>
        <p><?php _e('After creating your slideshow group, you will be able to:', 'esp-admin-manager'); ?></p>
        <ul>
            <li><?php _e('Add individual slides with images, titles, and descriptions', 'esp-admin-manager'); ?></li>
            <li><?php _e('Reorder slides by dragging and dropping', 'esp-admin-manager'); ?></li>
            <li><?php _e('Set links for slides to make them clickable', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use shortcodes to display the slideshow on any page or post', 'esp-admin-manager'); ?></li>
        </ul>
        
        <h4><?php _e('Shortcode Usage:', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Once created, you can display your slideshow using these shortcodes:', 'esp-admin-manager'); ?></p>
        <ul>
            <li><code>[esp_slideshow id="X"]</code> - <?php _e('Display specific slideshow by ID', 'esp-admin-manager'); ?></li>
            <li><code>[esp_slideshow tag="your-tag"]</code> - <?php _e('Display slideshow by tag', 'esp-admin-manager'); ?></li>
            <li><code>[esp_slideshow tag="home" autoplay="false"]</code> - <?php _e('Display with custom options', 'esp-admin-manager'); ?></li>
        </ul>
        
        <h4><?php _e('Available Options:', 'esp-admin-manager'); ?></h4>
        <ul>
            <li><strong>autoplay</strong> - <?php _e('true/false (default: true)', 'esp-admin-manager'); ?></li>
            <li><strong>duration</strong> - <?php _e('milliseconds between slides (default: 5000)', 'esp-admin-manager'); ?></li>
            <li><strong>show_nav</strong> - <?php _e('true/false to show navigation arrows (default: true)', 'esp-admin-manager'); ?></li>
            <li><strong>show_dots</strong> - <?php _e('true/false to show dot indicators (default: true)', 'esp-admin-manager'); ?></li>
            <li><strong>height</strong> - <?php _e('custom height in pixels (default: 400px)', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>

<style>
.required {
    color: #d63638;
}

.esp-form-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.esp-tags-help {
    margin-top: 10px;
    padding: 15px;
    background: #f9f9f9;
    border-left: 4px solid #0073aa;
}

.esp-tags-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.esp-tags-help ul {
    margin: 0;
    padding-left: 20px;
}

.esp-tags-help li {
    margin-bottom: 5px;
}

.esp-help-section {
    margin-top: 30px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.esp-help-section h3 {
    margin-top: 0;
    color: #0073aa;
}

.esp-help-section h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #333;
}

.esp-help-section ul {
    padding-left: 20px;
}

.esp-help-section li {
    margin-bottom: 8px;
}

.esp-help-section code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Form validation
    $('#add-group-form').on('submit', function(e) {
        var name = $('#group-name').val().trim();
        
        if (name === '') {
            e.preventDefault();
            alert('<?php _e('Please enter a group name.', 'esp-admin-manager'); ?>');
            $('#group-name').focus();
            return false;
        }
        
        // Show loading state
        var submitBtn = $(this).find('input[type="submit"]');
        submitBtn.prop('disabled', true).val('<?php _e('Creating...', 'esp-admin-manager'); ?>');
    });
    
    // Tag input helper
    $('#group-tags').on('input', function() {
        var tags = $(this).val();
        // Clean up tags (remove extra spaces, etc.)
        var cleanTags = tags.split(',').map(function(tag) {
            return tag.trim();
        }).filter(function(tag) {
            return tag !== '';
        }).join(', ');
        
        if (cleanTags !== tags) {
            $(this).val(cleanTags);
        }
    });
});
</script>
